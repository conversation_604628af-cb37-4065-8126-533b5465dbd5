import axios from 'axios'
import router from '../router/index'

// axios 配置
axios.defaults.baseURL = '/index.php'
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded'
axios.defaults.headers.get['Content-Type'] = 'application/x-www-form-urlencoded'

axios.interceptors.request.use(
  config => {
    // 请求拦截
    let formdata = new FormData()
    Object.keys(config.data).forEach(key => {
      formdata.append(key, config.data[key])
    })
    let sign = localStorage.getItem('userSign')
    if (sign && config.data.c != 'picker' && config.data.c != 'login') {
      formdata.append('union_sign', sign)
    }
    config.data = formdata
    return config
  },
  error => {
    return Promise.reject(error)
  },
)
axios.interceptors.response.use(
  response => {
    // 响应拦截
    let res = response.data
    let formdata = response.config.data
    let c = formdata.get('c')
    if (res.ret == -1 && c != 'login' && c != 'reg' && c != 'unitReg') {
      localStorage.removeItem('userSign')
      router.replace({ path: '/reg' })
    }
    return res
  },
  error => {
    return Promise.reject(error)
  },
)

export default axios
