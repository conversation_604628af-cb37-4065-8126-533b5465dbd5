/** @format */

// import { createRouter, createWebHistory } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'
import store from '@/store/index'
import axios from 'axios'
import OEUI from '@/oeui/js/oeui'

const routes = [
  {
    path: '/',
    name: 'index',
    component: () => import('../views/index.vue'),
    meta: {
      level: 0,
      isLogin: false,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/reg',
    name: 'Reg',
    component: () => import('../views/reg/reg.vue'),
    meta: {
      level: 1,
      isLogin: false,
      keepAlive: true,
      transitionName: '',
    },
  },
  {
    path: '/unitReg',
    name: 'UnitReg',
    component: () => import('../views/reg/unitReg.vue'),
    meta: {
      level: 1,
      isLogin: false,
      keepAlive: true,
      transitionName: '',
    },
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/reg/login.vue'),
    meta: {
      level: 1,
      isLogin: false,
      keepAlive: true,
      transitionName: '',
    },
  },
  {
    path: '/reg/wechat',
    name: 'RegWechat',
    component: () => import('../views/reg/wechat.vue'),
    meta: {
      level: 0,
      isLogin: false,
      keepAlive: false,
      transitionName: '',
    },
  },

  {
    path: '/home',
    name: 'Home',
    component: () => import('../views/index/home.vue'),
    meta: {
      level: 1,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  // {
  //   path: '/income',
  //   name: 'Income',
  //   component: () => import('../views/income/income.vue'),
  //   meta: {
  //     level: 1,
  //     isLogin: true,
  //     keepAlive: true,
  //     transitionName: '',
  //     isListScroll: true
  //   }
  // },
  {
    path: '/rwincome',
    name: 'Rwincome',
    component: () => import('../views/income/rwincome.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/rwincome/search',
    name: 'RwincomeSearch',
    component: () => import('../views/income/rwincome_search.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/withdraw',
    name: 'Sithdraw',
    component: () => import('../views/income/withdraw.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/withdraw/detail',
    name: 'WithdrawDetail',
    component: () => import('../views/income/withdraw_detail.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/withdraw/search',
    name: 'SithdrawSearch',
    component: () => import('../views/income/withdraw_search.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/account',
    name: 'Account',
    component: () => import('../views/income/account.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/group',
    name: 'Group',
    component: () => import('../views/income/group.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/cp',
    name: 'Cp',
    component: () => import('../views/cp/index.vue'),
    meta: {
      level: 1,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/edit',
    name: 'Edit',
    component: () => import('../views/cp/edit.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/edit/password',
    name: 'EditPassword',
    component: () => import('../views/cp/edit_password.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/user/userEdit',
    name: 'userEdit',
    component: () => import('../views/user/userEdit.vue'),
    meta: {
      level: 1,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/msg',
    name: 'Msg',
    component: () => import('../views/msg/index.vue'),
    meta: {
      level: 1,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
    },
  },
  {
    path: '/user',
    name: 'User',
    component: () => import('../views/user/index.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/poster_code',
    name: 'PosterCode',
    component: () => import('../views/cp/poster_code.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/user/detail',
    name: 'UserDetail',
    component: () => import('../views/user/detail.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/user/share',
    name: 'UserShare',
    component: () => import('../views/user/share.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/audituser',
    name: 'AuditUser',
    component: () => import('../views/user/audit_user.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/audituser/detail',
    name: 'AuditUserDetail',
    component: () => import('../views/user/audit_detail.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/afteruser',
    name: 'AfterUser',
    component: () => import('../views/afteruser/index.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/afteruser/detail',
    name: 'AfterUserDetail',
    component: () => import('../views/afteruser/detail.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/myafteruser',
    name: 'MyAfterUser',
    component: () => import('../views/afteruser/myafteruser.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/match/log',
    name: 'MatchLog',
    component: () => import('../views/afteruser/match_log.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/match/result',
    name: 'MatchResult',
    component: () => import('../views/afteruser/match_result.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/party',
    name: 'Party',
    component: () => import('../views/party/index.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/party/detail',
    name: 'PartyDetail',
    component: () => import('../views/party/detail.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/myparty',
    name: 'Myparty',
    component: () => import('../views/party/myparty.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/party/bmlist',
    name: 'PartyBmlist',
    component: () => import('../views/party/bmlist.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
      isListScroll: false,
    },
  },
  {
    path: '/activity',
    name: 'Activity',
    component: () => import('../views/activity/index.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },

  {
    path: '/activity/detail',
    name: 'ActivityDetail',
    component: () => import('../views/activity/detail.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/myactivity',
    name: 'Myactivity',
    component: () => import('../views/activity/myactivity.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/activity/bmlist',
    name: 'ActivityBmlist',
    component: () => import('../views/activity/bmlist.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
      isListScroll: true,
    },
  },

  {
    path: '/level1',
    name: 'Level1',
    component: () => import('../views/cp/level/level1.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/level2',
    name: 'Level2',
    component: () => import('../views/cp/level/level2.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/level3',
    name: 'Level3',
    component: () => import('../views/cp/level/level3.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/level4',
    name: 'Level4',
    component: () => import('../views/cp/level/level4.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/myuser',
    name: 'Myuser',
    component: () => import('../views/cp/myuser.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/myunion',
    name: 'Myunion',
    component: () => import('../views/cp/myunion.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/rwreg',
    name: 'Rwreg',
    component: () => import('../views/cp/rwreg.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/rwbuy',
    name: 'Rwbuy',
    component: () => import('../views/cp/rwbuy.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/teamunion',
    name: 'Teamunion',
    component: () => import('../views/cp/team/teamunion.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/teamuser',
    name: 'Teamuser',
    component: () => import('../views/cp/team/teamuser.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/teamlog',
    name: 'Teamlog',
    component: () => import('../views/cp/team/teamlog.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/crmincome',
    name: 'Crmincome',
    component: () => import('../views/cp/income.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/myafter',
    name: 'MyAfter',
    component: () => import('../views/cp/afteruser.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/myaudit',
    name: 'MyAudit',
    component: () => import('../views/cp/audituser.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/crmmember',
    name: 'Crmmember',
    component: () => import('../views/cp/crm/crmmember.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: true,
      transitionName: '',
      isListScroll: true,
    },
  },
  {
    path: '/crmuser/detail',
    name: 'crmuserDetail',
    component: () => import('../views/cp/crm/crm_detail.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/crmuser/edit',
    name: 'crmuserEdit',
    component: () => import('../views/cp/crm/user_edit.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/crmuser/add',
    name: 'crmuserAdd',
    component: () => import('../views/cp/crm/user_add.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  //激活页面
  {
    path: '/active',
    name: 'Active',
    component: () => import('../views/active/index.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/kefu',
    name: 'Kefu',
    component: () => import('../views/cp/kefu.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/poster',
    name: 'Poster',
    component: () => import('../views/union/poster.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/slogan',
    name: 'Slogan',
    component: () => import('../views/union/slogan.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/idrz',
    name: 'Idrz',
    component: () => import('../views/cp/idrz/idrz.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/idrz2',
    name: 'Idrz2',
    component: () => import('../views/cp/idrz/idrz2.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/idrz/result',
    name: 'IdrzResult',
    component: () => import('../views/cp/idrz/idrz_result.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/loveeval',
    name: 'Loveeval',
    component: () => import('../views/loveeval/loveeval.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/lottery',
    name: 'Lottery',
    component: () => import('../views/loveeval/lottery.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },
  {
    path: '/bazi',
    name: 'Bazi',
    component: () => import('../views/loveeval/bazi.vue'),
    meta: {
      level: 2,
      isLogin: true,
      keepAlive: false,
      transitionName: '',
    },
  },

  {
    //404 必须放在最后面
    path: '/:pathMatch(.*)*',
    name: '404',
    component: () => import('../views/404.vue'),
    meta: {
      level: 100,
      isLogin: false,
      keepAlive: false,
      transitionName: '',
    },
  },
]

const router = createRouter({
  //history: createWebHistory(process.env.BASE_URL),
  history: createWebHashHistory(), // hash模式
  routes,
})

const powerList = ['Home', 'Rwincome', 'Myuser', 'Poster', 'Active']
router.beforeEach((to, from, next) => {
  //保存openid
  if (to.query.openid) {
    sessionStorage.setItem('openid', to.query.openid)
  }
  //保存tg_unionid
  if (to.query.tg_unionid) {
    sessionStorage.setItem('tg_unionid', to.query.tg_unionid)
  }
  //保存tg_uid
  if (to.query.tg_uid) {
    sessionStorage.setItem('tg_uid', to.query.tg_uid)
  }

  if (to.query.union_sign) {
    localStorage.setItem('userSign', to.query.union_sign)
    store.dispatch('getUserInfo')
  }
  let sign = localStorage.getItem('userSign')
  if (sign) {
    //点击tabear 更新数据
    if (to.name == 'Home') store.dispatch('getTj')
    if (to.name == 'Income') store.dispatch('getIncome')
    if (!to.meta.isLogin && from.meta.isLogin) {
      next({ path: from.path, replace: true })
    } else if (!to.meta.isLogin) {
      next({ path: '/home', replace: true })
    } else {
      next()
    }
  } else {
    if (powerList.includes(to.name) && to.query.union_sign) {
      localStorage.setItem('userSign', to.query.union_sign)
      store.dispatch('getUserInfo').then(() => {
        next()
      })
    } else if (to.meta.isLogin) {
      sessionStorage.removeItem('setRouter')
      sessionStorage.removeItem('routerArr')
      sessionStorage.setItem('replaceLogin', to.name)
      next({ path: '/reg', replace: true })
    } else {
      next()
    }
  }
})
export default router
