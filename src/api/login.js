import axios from '@/utils/axios'

export const getXieyi = params => {
  return axios.post('', {
    m: 'vuewap',
    c: 'htmllabel',
    a: 'get',
    ...params,
  })
}

export const loginPassword = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'login',
    a: 'post',
    ...params,
  })
}

export const loginReg = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'reg2',
    a: 'mobile',
    ...params,
  })
}

export const loginOut = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'login',
    a: 'logout',
    ...params,
  })
}

export const codeSend = params => {
  return axios.post('', {
    m: 'vuewap',
    c: 'picker',
    a: 'sendsms',
    ...params,
  })
}

export const checkWxauth = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'wxauth',
    ...params,
  })
}

export const wxauthCallback = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'wxauth',
    a: 'callback',
    ...params,
  })
}

export const checkEntername = params => {
  return axios.post('', {
    m: 'union',
    c: 'reg2',
    a: 'checkentername',
    ...params,
  })
}
